#include "Utils.hpp"
#include <fstream>
#include <iostream>
#include <cmath>
#include <algorithm>
#include <thread>
#include <unordered_set>

void writeResultsToCSV(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开CSV文件: " << filename << std::endl;
        return;
    }

    file << "frame,id,x,y,z\n";
    for (size_t frame = 0; frame < tracks.size(); ++frame) {
        for (const auto& obj : tracks[frame]) {
            file << frame << "," << obj.id << ","
                 << obj.position.x() << "," << obj.position.y() << "," << obj.position.z() << "\n";
        }
    }

    file.close();
    std::cout << "CSV写入完成: " << filename << std::endl;
}

void writeResultsToPLY(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开PLY文件: " << filename << std::endl;
        return;
    }

    // 统计总点数
    size_t total_points = 0;
    for (const auto& frame_data : tracks)
        total_points += frame_data.size();

    // 写入PLY头部
    file << "ply\nformat ascii 1.0\n";
    file << "element vertex " << total_points << "\n";
    file << "property float x\nproperty float y\nproperty float z\n";
    file << "property uchar red\nproperty uchar green\nproperty uchar blue\n";
    file << "end_header\n";

    // 写入每个点
    for (const auto& frame_data : tracks) {
        for (const auto& obj : frame_data) {
            // 通过 id 映射颜色（可改进）
            uint8_t r = (obj.id * 53) % 256;
            uint8_t g = (obj.id * 97) % 256;
            uint8_t b = (obj.id * 151) % 256;

            file << obj.position.x() << " " << obj.position.y() << " " << obj.position.z() << " ";
            file << static_cast<int>(r) << " " << static_cast<int>(g) << " " << static_cast<int>(b) << "\n";
        }
    }

    file.close();
    std::cout << "PLY写入完成: " << filename << std::endl;
}


void writePointsToCSV(const std::vector<std::vector<Point>>& points, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开CSV文件: " << filename << std::endl;
        return;
    }

    file << "frame,id,x,y,z\n";
    for (size_t frame = 0; frame < points.size(); ++frame) {
        for (const auto& p : points[frame]) {
            if (p.label == -1){
                continue;
            }
            file << p.frame << "," << p.label << ","
                 << p.position[0] << "," << p.position[1] << "," << p.position[2] << "\n";
        }
    }

    file.close();
    std::cout << "原始点迹写入完成: " << filename << std::endl;
}

float distance(const Point& a, const Point& b) {
    float dx = a.position[0] - b.position[0];
    float dy = a.position[1] - b.position[1];
    float dz = a.position[2] - b.position[2];
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

// 并行寻找邻居
void findNeighborsParallel(const std::vector<Point>& points, float eps, std::vector<std::vector<size_t>>& neighbors, int start, int end) {
    for (int i = start; i < end; ++i) {
        for (size_t j = 0; j < points.size(); ++j) {
            if (i == j) continue;
            if (distance(points[i], points[j]) <= eps) {
                neighbors[i].push_back(j);
            }
        }
    }
}

std::vector<Point> clusterDetections_DBSCAN(const std::vector<Point>& points, float eps, int minPts, int numThreads) {
    const size_t N = points.size();
    if (N == 0) return {};

    // Step 1: 多线程寻找邻居
    std::vector<std::vector<size_t>> neighbors(N);
    std::vector<std::thread> threads;
    int blockSize = N / numThreads;

    for (int t = 0; t < numThreads; ++t) {
        int start = t * blockSize;
        int end = (t == numThreads - 1) ? N : (t + 1) * blockSize;
        threads.emplace_back(findNeighborsParallel, std::cref(points), eps, std::ref(neighbors), start, end);
    }

    for (auto& thread : threads) {
        thread.join();
    }

    // Step 2: DBSCAN 聚类
    std::vector<bool> visited(N, false);
    std::vector<bool> clustered(N, false);
    std::vector<Point> clusteredPoints;

    for (size_t i = 0; i < N; ++i) {
        if (visited[i]) continue;
        visited[i] = true;

        if (neighbors[i].size() + 1 < static_cast<size_t>(minPts)) {
            continue; // 噪声点
        }

        // 新建聚类
        std::vector<size_t> cluster_indices;
        std::unordered_set<size_t> seeds(neighbors[i].begin(), neighbors[i].end());
        seeds.insert(i); // 包括自己

        for (auto it = seeds.begin(); it != seeds.end(); ++it) {
            size_t idx = *it;
            if (!visited[idx]) {
                visited[idx] = true;
                if (neighbors[idx].size() + 1 >= static_cast<size_t>(minPts)) {
                    for (auto n : neighbors[idx]) {
                        seeds.insert(n);
                    }
                }
            }
            if (!clustered[idx]) {
                cluster_indices.push_back(idx);
                clustered[idx] = true;
            }
        }

        // 计算聚类中心
        std::array<float, 3> center_pos{0, 0, 0};
        std::array<float, 3> center_vel{0, 0, 0};
        for (size_t idx : cluster_indices) {
            center_pos[0] += points[idx].position[0];
            center_pos[1] += points[idx].position[1];
            center_pos[2] += points[idx].position[2];

            center_vel[0] += points[idx].velocity[0];
            center_vel[1] += points[idx].velocity[1];
            center_vel[2] += points[idx].velocity[2];
        }

        float size = static_cast<float>(cluster_indices.size());
        for (int d = 0; d < 3; ++d) {
            center_pos[d] /= size;
            center_vel[d] /= size;
        }

        Point cluster_center;
        cluster_center.position = center_pos;
        cluster_center.velocity = center_vel;
        cluster_center.type = points[i].type;
        cluster_center.frame = points[i].frame;
        cluster_center.label = points[i].label;

        clusteredPoints.push_back(cluster_center);
    }

    return clusteredPoints;
}