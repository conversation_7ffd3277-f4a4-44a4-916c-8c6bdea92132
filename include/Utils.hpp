#ifndef UTILS_HPP
#define UTILS_HPP

#include <vector>
#include <string>
#include <eigen3/Eigen/Dense>
#include "PointTracker.hpp"  // 包含 TrackedObject 结构定义

// 点迹跟踪算法输入结构体
struct Point {
    std::array<float,3> position;    // 三维坐标 (x, y, z)
    std::array<float,3> velocity;    // 三维速度分量 (vx, vy, vz)
    int type;                        // 目标类型
    int frame;                       // 帧数
    int label;                       // 标签（用于聚类）
};

// 跟踪结果输出
struct TrackResult {
    Eigen::Vector3f position;
    Eigen::Vector3f velocity;
    int id;
};

void writeResultsToCSV(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename);
void writeResultsToPLY(const std::vector<std::vector<TrackResult>>& tracks, const std::string& filename);
void writePointsToCSV(const std::vector<std::vector<Point>>& points, const std::string& filename);


float distance(const Point& a, const Point& b);
void findNeighborsParallel(const std::vector<Point>& points, float eps, std::vector<std::vector<size_t>>& neighbors, int start, int end);
// DBSCAN 聚类
std::vector<Point> clusterDetections_DBSCAN(const std::vector<Point>& points, float eps, int minPts, int numThreads);

#endif  // UTILS_HPP
