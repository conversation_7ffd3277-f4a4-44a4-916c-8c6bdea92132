#include <iostream>
#include <vector>
#include <fstream>
#include <sstream>
#include <map>
#include <algorithm>
#include "PointTracker.hpp"
#include "Utils.hpp"

// CSV数据读取函数
std::vector<std::vector<Point>> loadRadarDataFromCSV(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开CSV文件: " << filename << std::endl;
        return {};
    }

    std::string line;
    std::getline(file, line); // 跳过标题行

    std::map<int, std::vector<Point>> frame_data;
    int max_frame = 0;

    while (std::getline(file, line)) {
        if (line.empty()) continue;

        std::stringstream ss(line);
        std::string cell;
        std::vector<std::string> row;

        // 解析CSV行
        while (std::getline(ss, cell, ',')) {
            // 去除前后空格
            cell.erase(0, cell.find_first_not_of(" \t"));
            cell.erase(cell.find_last_not_of(" \t") + 1);
            row.push_back(cell);
        }

        if (row.size() < 12) continue; // 确保有足够的列

        try {
            Point point;
            point.velocity[0] = std::stof(row[0]); // vx
            point.velocity[1] = std::stof(row[1]); // vy
            point.velocity[2] = std::stof(row[2]); // vz
            point.position[0] = std::stof(row[3]); // x
            point.position[1] = std::stof(row[4]); // y
            point.position[2] = std::stof(row[5]); // z
            // fMV, fMR, fMA, fME 暂时不使用
            point.type = std::stoi(row[10]); // type
            point.frame = std::stoi(row[11]); // frame
            point.label = -1; // 初始化为杂波标签

            frame_data[point.frame].push_back(point);
            max_frame = std::max(max_frame, point.frame);
        } catch (const std::exception& e) {
            std::cerr << "解析CSV行时出错: " << line << std::endl;
            continue;
        }
    }

    file.close();

    // 创建连续的帧数据，缺失的帧用空向量填充
    std::vector<std::vector<Point>> all_detections(max_frame + 1);
    for (const auto& [frame_num, points] : frame_data) {
        all_detections[frame_num] = points;
    }

    std::cout << "从CSV文件读取完毕，总帧数: " << max_frame + 1
              << "，有数据的帧数: " << frame_data.size() << std::endl;

    return all_detections;
}

int main() {
    const std::string csv_file = "python/radar_simulation_data.csv";

    // ---------- 1. 从CSV文件读取雷达数据 ----------
    std::vector<std::vector<Point>> all_detections = loadRadarDataFromCSV(csv_file);

    if (all_detections.empty()) {
        std::cerr << "无法读取数据，程序退出" << std::endl;
        return -1;
    }

    // 统计有数据的帧
    int frames_with_data = 0;
    for (const auto& frame : all_detections) {
        if (!frame.empty()) frames_with_data++;
    }

    std::cout << "数据统计 - 总帧数: " << all_detections.size()
              << ", 有数据帧数: " << frames_with_data << std::endl;

    // ---------- 2. 初始化跟踪器 ----------
    // 调整参数适应稀疏数据：增大max_age和distance_threshold，减小min_hits
    PointTracker tracker(10, 50, 200.0f, 1);
    std::vector<std::vector<TrackResult>> tracked_results;

    // ---------- 3. 遍历每一帧 ------------
    int num_frames = all_detections.size();
    for (int frame = 0; frame < num_frames; ++frame) {
        const auto& detections_raw = all_detections[frame];

        std::vector<Point> detections;

        // 如果当前帧有数据，则处理数据
        if (!detections_raw.empty()) {
            for (const auto& point : detections_raw) {
                Point p = point; // 直接使用从CSV读取的点
                p.frame = frame; // 确保帧号正确
                p.label = -1;    // 初始化为杂波标签
                detections.push_back(p);
            }

            // 对检测进行聚类
            detections = clusterDetections_DBSCAN(detections, 30.0f, 1, 4);
        }
        // 如果当前帧没有数据，detections使用空向量
        else {
            detections.clear();
        }

        // 更新跟踪器（即使是空数据也要更新，以便跟踪器进行预测）
        std::vector<TrackResult> tracks = tracker.update(detections);
        tracked_results.push_back(tracks);

        // 显示进度
        if (frame % 50 == 0 || !detections_raw.empty()) {
            std::cout << "Processing frame " << frame << "/" << num_frames
                      << ", detections: " << detections.size()
                      << ", tracks: " << tracks.size() << std::endl;
        }
    }

    // ---------- 4. 输出结果 ----------
    writeResultsToCSV(tracked_results, "output.csv");
    writePointsToCSV(all_detections, "real.csv");

    std::cout << "跟踪完成，结果已保存到 output.csv 和 real.csv" << std::endl;
    return 0;
}
